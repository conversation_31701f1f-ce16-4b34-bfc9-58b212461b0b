import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:kairos/features/authentication/presentation/pages/accueil/accueil_widgets/app_activated.widget.dart';
import 'package:kairos/features/authentication/presentation/pages/accueil/accueil_widgets/code_activation.widget.dart';
import 'package:kairos/features/authentication/presentation/pages/accueil/accueil_widgets/phone_authentication.widget.dart';
import 'package:kairos/features/authentication/presentation/pages/accueil/accueil_widgets/home_slide.widget.dart';
import 'package:kairos/features/authentication/presentation/pages/accueil/accueil_widgets/enter_fullname.widget.dart';
import 'package:kairos/features/authentication/presentation/bloc/cubit/phone_authentication_cubit.dart';
import 'package:kairos/features/authentication/presentation/bloc/state/auth_state.dart';

class AccueilPage extends StatefulWidget{
  const AccueilPage({super.key});

  @override
  State<AccueilPage> createState() => _AccueilPageState();
}

class _AccueilPageState extends State<AccueilPage>{
  final PageController _pageController = PageController();
  String? _phoneNumber; // Add state variable for phone number

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args is Map && args['goToPhoneAuth'] == true) {
      // Jump to the PhoneAuthentication page (index 2)
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _pageController.jumpToPage(1);
      });
    }
  }

  @override
  Widget build(BuildContext context){
    return Scaffold(
      body: BlocConsumer<PhoneAuthenticationCubit, AuthState>( // Consume PhoneAuthenticationCubit state
          listener: (context, state) {
            // Update phone number state when SMS is sent or fails
            if (state is AuthSmsSent) {
              setState(() {
                _phoneNumber = state.phoneNumber;
              });
            } else if (state is AuthSmsError) {
               setState(() {
                _phoneNumber = state.phoneNumber;
              });
            }
          },
          builder: (context, state) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Spacer(),
                SizedBox(height: 40),
               Expanded(
                // height: MediaQuery.of(context).size.height * .80,
                child: PageView(
                  physics: const NeverScrollableScrollPhysics(),
                  controller: _pageController,
                  children: [
                    HomeSlideWidget(pageController: _pageController),
                    PhoneAuthentication(pageController: _pageController), // PhoneAuthentication is now a direct child
                    CodeActivationWidget(pageController: _pageController, phoneNumber: _phoneNumber ?? ''), // Pass phone number
                    EnterFullnameWidget(pageController: _pageController), // Add full name entry before phone authentication
                    AppActivatedWidget(pageController: _pageController),
                  ],
                ),
               ),
               // SizedBox(height: 40),
               // Spacer(flex: 1,),
               Image.asset("assets/images/logo_footer.png", height: 19),
               SizedBox(height: 10)
              ],
            );
          },
        ),
      );

  }
}
