import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:kairos/core/widgets/common/snackbar_widget.dart';
import 'package:kairos/features/profile/domain/entities/carte_virtuelle_entity.dart';

/// Callback function type for handling scanned QR code data
typedef QRScanCallback = void Function(String matricule);

/// Callback function type for handling manual input data
typedef ManualInputCallback = void Function(String matricule);

/// Shared service for QR code scanning with manual input fallback
class QRScannerService {
  /// Launch QR code scanner with manual input option
  /// 
  /// [context] - BuildContext for navigation and UI operations
  /// [onQRScanned] - Callback when QR code is successfully scanned
  /// [onManualInput] - Callback when manual input is confirmed
  /// [onError] - Optional callback for error handling
  static Future<void> launchScanner({
    required BuildContext context,
    required QRScanCallback onQRScanned,
    required ManualInputCallback onManualInput,
    VoidCallback? onError,
  }) async {
    try {
      await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (scannerContext) => _QRScannerScreen(
            onQRScanned: onQRScanned,
            onManualInput: onManualInput,
            onError: onError,
          ),
        ),
      );
    } catch (e) {
      debugPrint('QR Scanner error: $e');
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'ouverture du scanner QR: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
      
      onError?.call();
    }
  }

  /// Extract matricule (codeUtilisateur) from scanned QR code JSON
  static String? extractMatriculeFromQR(String scannedValue) {
    try {
      final Map<String, dynamic> jsonMap = json.decode(scannedValue);
      final carteVirtuelle = CarteVirtuelleEntity.fromJson(jsonMap);
      return carteVirtuelle.codeUtilisateur;
    } catch (e) {
      debugPrint('Error parsing scanned QR JSON: $e');
      return null;
    }
  }
}

/// Internal QR Scanner Screen widget
class _QRScannerScreen extends StatefulWidget {
  final QRScanCallback onQRScanned;
  final ManualInputCallback onManualInput;
  final VoidCallback? onError;

  const _QRScannerScreen({
    required this.onQRScanned,
    required this.onManualInput,
    this.onError,
  });

  @override
  State<_QRScannerScreen> createState() => _QRScannerScreenState();
}

class _QRScannerScreenState extends State<_QRScannerScreen> {
  late MobileScannerController _controller;
  bool _isManualInputVisible = false;
  bool _isScanningEnabled = true;
  final TextEditingController _matriculeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controller = MobileScannerController(
      detectionSpeed: DetectionSpeed.normal,
      formats: const [BarcodeFormat.qrCode],
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _matriculeController.dispose();
    super.dispose();
  }

  void _handleBarcodeDetection(BarcodeCapture capture) {
    // Only process if scanning is enabled (manual input not visible)
    if (!_isScanningEnabled) return;

    final String? scannedValue = capture.barcodes.first.rawValue;
    debugPrint('QRScannerService: Scanned value: $scannedValue');
    
    if (scannedValue != null && scannedValue.isNotEmpty) {
      final String? matricule = QRScannerService.extractMatriculeFromQR(scannedValue);
      
      if (matricule != null && matricule.isNotEmpty) {
        Navigator.of(context).pop(); // Close scanner
        widget.onQRScanned(matricule);
      } else {
        _showErrorSnackbar('Code QR invalide. Veuillez réessayer.');
      }
    }
  }

  void _showManualInputForm() {
    setState(() {
      _isManualInputVisible = true;
      _isScanningEnabled = false; // Disable QR scanning
    });
  }

  void _hideManualInputForm() {
    setState(() {
      _isManualInputVisible = false;
      _isScanningEnabled = true; // Re-enable QR scanning
      _matriculeController.clear();
    });
  }

  void _confirmManualInput() {
    final matricule = _matriculeController.text.trim();
    
    if (matricule.isEmpty) {
      _showErrorSnackbar('Veuillez saisir le matricule de l\'étudiant.');
      return;
    }

    Navigator.of(context).pop(); // Close scanner
    widget.onManualInput(matricule);
  }

  void _showErrorSnackbar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        CustomSnackbar(message: message, isError: true).getSnackBar(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // QR Scanner
          MobileScanner(
            controller: _controller,
            onDetect: _handleBarcodeDetection,
          ),
          
          // Top bar with close button
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            left: 16,
            right: 16,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Colors.white, size: 30),
                ),
                const Text(
                  'Scanner le code QR',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 48), // Balance the close button
              ],
            ),
          ),

          // Manual input button
          Positioned(
            bottom: 120,
            left: 20,
            right: 20,
            child: Container(
              width: double.infinity,
              height: 50,
              decoration: BoxDecoration(
                color: const Color(0xFF06B6E4),
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF16196E).withValues(alpha: 0.08),
                    offset: const Offset(0, 6),
                    blurRadius: 16,
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(10),
                  onTap: _showManualInputForm,
                  child: const Center(
                    child: Text(
                      'Saisir le matricule de l\'étudiant',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Manual input overlay
          if (_isManualInputVisible)
            Container(
              color: Colors.black.withValues(alpha: 0.8),
              child: Center(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        'Saisir le matricule',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(height: 20),
                      
                      // Text field for matricule input
                      TextField(
                        controller: _matriculeController,
                        decoration: InputDecoration(
                          hintText: 'Matricule de l\'étudiant',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: const BorderSide(color: Color(0xFF06B6E4)),
                          ),
                        ),
                        autofocus: true,
                      ),
                      
                      const SizedBox(height: 20),
                      
                      // Action buttons
                      Row(
                        children: [
                          Expanded(
                            child: TextButton(
                              onPressed: _hideManualInputForm,
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  side: const BorderSide(color: Colors.grey),
                                ),
                              ),
                              child: const Text(
                                'Annuler',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 15),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: _confirmManualInput,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF06B6E4),
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                              child: const Text(
                                'Confirmer',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
