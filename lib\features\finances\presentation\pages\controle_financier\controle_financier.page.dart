import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/widgets/common/hero_widget.dart';
import 'package:kairos/core/enums/header_enums.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/finances/presentation/bloc/finances_cubit.dart';
import 'package:kairos/features/finances/presentation/bloc/finances_state.dart';
import 'package:kairos/features/profile/domain/entities/carte_virtuelle_entity.dart'; // Import CarteVirtuelleEntity
import 'package:kairos/features/profile/presentation/bloc/profile_cubit.dart'; // Import ProfileCubit
import 'package:kairos/features/profile/presentation/bloc/profile_state.dart';
import 'package:kairos/core/widgets/common/snackbar_widget.dart';
import 'package:kairos/core/widgets/indicators/custom_spinner.dart';

import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:kairos/core/di/injection_container.dart';

import 'package:kairos/features/finances/presentation/pages/finances/finances_widgets/finance_item.widget.dart';
import 'package:kairos/core/services/qr_scanner_service.dart'; // Import QR Scanner Service

class ControleFinancierPage extends StatefulWidget {
    final EtablissementUtilisateur school;
    final String matricule;

    const ControleFinancierPage({
        super.key,
        required this.school,
        required this.matricule,
    });

    @override
    State<ControleFinancierPage> createState() => _ControleFinancierPageState();
}

class _ControleFinancierPageState extends State<ControleFinancierPage> {
    bool _isUnpaidFeesExpanded = false;
    String _currentMatricule; // State variable for current matricule
    CarteVirtuelleEntity? _currentCarteVirtuelle; // State variable for currently displayed data

    _ControleFinancierPageState() : _currentMatricule = '';

    @override
    void initState() {
        super.initState();
        _currentMatricule = widget.matricule; // Initialize with provided matricule
        _initializeFinancialCheck();
    }

    Future<void> _initializeFinancialCheck() async {
        // Load finances data with financial status check
        final financesCubit = context.read<FinancesCubit>();
        final profileCubit = context.read<ProfileCubit>(); // Inject ProfileCubit
        final authLocalDataSource = sl<AuthLocalDataSource>();

        // Use the current matricule directly
        final codeEtudiant = _currentMatricule;

        if (codeEtudiant.isNotEmpty) {
            // Get phone number from SharedPreferences
            final phoneNumber = await authLocalDataSource.getPhoneNumber();

            if (phoneNumber != null) {
                // Load finances data with financial status
                financesCubit.loadFinancesDataWithStatus(
                    codeEtab: widget.school.codeEtab,
                    telephone: phoneNumber,
                    codeEtudiant: codeEtudiant,
                    codeUtilisateur: widget.school.codeUtilisateur,
                );

                // Invoke getCarteVirtuelle from ProfileCubit
                profileCubit.getCarteVirtuelle(
                    codeEtab: widget.school.codeEtab,
                    telephone: phoneNumber,
                    codeEtudiant: codeEtudiant,
                    codeUtilisateur: widget.school.codeUtilisateur,
                );
            }
        }
    }

    @override
    Widget build(BuildContext context) {
        return Scaffold(
            body: CustomScrollView(
                slivers: [
                    // Hero Section
                    SliverToBoxAdapter(
                        child: HeroWidget(
                            pageSection: HeaderEnum.controleFinancier,
                            etablissementUser: widget.school,
                            carteVirtuelle: _currentCarteVirtuelle,
                        ),
                    ),

                    // Main Content
                    SliverToBoxAdapter(
                        child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 7.0),
                            child: Column(
                                children: [
                                    const SizedBox(height: 15),
                                    // Financial Status Section
                                    BlocListener<ProfileCubit, ProfileState>(
                                        listener: (context, profileState) {
                                            if (profileState is CarteVirtuelleLoaded) {
                                                setState(() {
                                                    _currentCarteVirtuelle = profileState.carteVirtuelle;
                                                });
                                            } else if (profileState is CarteVirtuelleError) {
                                                // Optionally show an error message if virtual card loading fails
                                                ScaffoldMessenger.of(context).showSnackBar(
                                                    CustomSnackbar(message: 'Erreur lors du chargement de la carte virtuelle: ${profileState.message}', isError: true).getSnackBar(),
                                                );
                                            }
                                        },
                                        child: BlocBuilder<FinancesCubit, FinancesState>(
                                            builder: (context, financesState) {
                                                if (financesState is FinancesLoading) {
                                                    return const Center(
                                                        child: CustomSpinner(),
                                                    );
                                                } else if (financesState is FinancesLoaded) {
                                                    final financialStatus = financesState.financialStatus;
                                                    if (financialStatus != null) {
                                                        return _buildStatusSection(financialStatus.enRegle);
                                                    }
                                                } else if (financesState is FinancesError) {
                                                    return Center(
                                                        child: Text(
                                                            'Erreur: ${financesState.message}',
                                                            style: const TextStyle(color: Colors.red),
                                                        ),
                                                    );
                                                }
                                                return const SizedBox.shrink();
                                            },
                                        ),
                                    ),

                                    const SizedBox(height: 15),

                                    // Unpaid Fees Section (only show if not "en règle")
                                    BlocBuilder<FinancesCubit, FinancesState>(
                                        builder: (context, financesState) {
                                            if (financesState is FinancesLoaded &&
                                                    financesState.financialStatus != null &&
                                                    !financesState.financialStatus!.enRegle) {
                                                debugPrint('financesState.unpaidFeesResponse -------->: ${(financesState as FinancesLoaded?)?.unpaidFeesResponse})}');
                                                return _buildUnpaidFeesSection();
                                            }
                                            return const SizedBox.shrink();
                                        },
                                    ),

                                    const SizedBox(height: 20),

                                    // New Control Button
                                    _buildNewControlBtn(),

                                    const SizedBox(height: 20),
                                ],
                            ),
                        ),
                    ),
                ],
            ),
        );
    }

    Widget _buildStatusSection(bool enRegle) {
        if (enRegle) {
            return _buildSuccessStatus();
        } else {
            return _buildAlertStatus();
        }
    }

    Widget _buildSuccessStatus() {
        return Container(
            width: 335,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
                color: const Color(0xFFE8F5E8),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: const Color(0xFF4CAF50), width: 1),
            ),
            child: Column(
                children: [
                    // Success Icon
                    Container(
                        width: 60,
                        height: 60,
                        decoration: const BoxDecoration(
                            color: Color(0xFF4CAF50),
                            shape: BoxShape.circle,
                        ),
                        child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 30,
                        ),
                    ),
                    const SizedBox(height: 15),

                    // Student Info
                    if (_currentCarteVirtuelle != null) ...[
                        Text(
                            '${_currentCarteVirtuelle!.prenom} ${_currentCarteVirtuelle!.nom}',
                            style: const TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w700,
                                fontSize: 18,
                                color: Color(0xFF2E7D32),
                            ),
                        ),
                        const SizedBox(height: 5),
                        Text(
                            'Matricule: ${_currentCarteVirtuelle!.codeUtilisateur}',
                            style: const TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w500,
                                fontSize: 14,
                                color: Color(0xFF666666),
                            ),
                        ),
                        const SizedBox(height: 15),
                    ],

                    // Status Message
                    const Text(
                        'ÉTUDIANT EN RÈGLE',
                        style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w700,
                            fontSize: 16,
                            color: Color(0xFF2E7D32),
                        ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                        'Tous les frais de scolarité ont été payés',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            fontSize: 14,
                            color: Color(0xFF666666),
                        ),
                    ),
                ],
            ),
        );
    }

    Widget _buildAlertStatus() {
        return Container(
            width: 335,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
                color: const Color(0xFFFFF3E0),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: const Color(0xFFFF9800), width: 1),
            ),
            child: Column(
                children: [
                    // Alert Icon
                    Container(
                        width: 60,
                        height: 60,
                        decoration: const BoxDecoration(
                            color: Color(0xFFFF9800),
                            shape: BoxShape.circle,
                        ),
                        child: const Icon(
                            Icons.warning,
                            color: Colors.white,
                            size: 30,
                        ),
                    ),
                    const SizedBox(height: 15),

                    // Student Info
                    if (_currentCarteVirtuelle != null) ...[
                        Text(
                            '${_currentCarteVirtuelle!.prenom} ${_currentCarteVirtuelle!.nom}',
                            style: const TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w700,
                                fontSize: 18,
                                color: Color(0xFFE65100),
                            ),
                        ),
                        const SizedBox(height: 5),
                        Text(
                            'Matricule: ${_currentCarteVirtuelle!.codeUtilisateur}',
                            style: const TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w500,
                                fontSize: 14,
                                color: Color(0xFF666666),
                            ),
                        ),
                        const SizedBox(height: 15),
                    ],

                    // Status Message
                    const Text(
                        'FRAIS IMPAYÉS DÉTECTÉS',
                        style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w700,
                            fontSize: 16,
                            color: Color(0xFFE65100),
                        ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                        'Cet étudiant a des frais de scolarité en attente',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            fontSize: 14,
                            color: Color(0xFF666666),
                        ),
                    ),
                ],
            ),
        );
    }

    Widget _buildUnpaidFeesSection() {
        return BlocBuilder<FinancesCubit, FinancesState>(
            builder: (context, financesState) {
                if (financesState is FinancesLoaded && financesState.unpaidFeesResponse != null) {
                    final unpaidFees = financesState.unpaidFeesResponse!.fraisImPayes;

                    return Column(
                        children: [
                            // Header with toggle button
                            Container(
                                width: 335,
                                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                                decoration: BoxDecoration(
                                    color: const Color(0xFFFFF3E0),
                                    borderRadius: BorderRadius.circular(10),
                                    border: Border.all(color: const Color(0xFFFF9800), width: 1),
                                ),
                                child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                        const Text(
                                            'Frais impayés',
                                            style: TextStyle(
                                                fontFamily: 'Inter',
                                                fontWeight: FontWeight.w600,
                                                fontSize: 16,
                                                color: Color(0xFFE65100),
                                            ),
                                        ),
                                        GestureDetector(
                                            onTap: () {
                                                setState(() {
                                                    _isUnpaidFeesExpanded = !_isUnpaidFeesExpanded;
                                                });
                                            },
                                            child: Icon(
                                                _isUnpaidFeesExpanded ? Icons.expand_less : Icons.expand_more,
                                                color: const Color(0xFFE65100),
                                            ),
                                        ),
                                    ],
                                ),
                            ),

                            // Expandable unpaid fees list
                            AnimatedContainer(
                                duration: const Duration(milliseconds: 300),
                                height: _isUnpaidFeesExpanded ? null : 0,
                                child: _isUnpaidFeesExpanded
                                        ? Column(
                                                children: unpaidFees?.map((fee) {
                                                    return FinanceItem(
                                                        intituleFrais: fee.intituleFrais,
                                                        montantFrais: (fee.montantFrais ?? 0).toInt(),
                                                        status: 'Aucune quittance générée',
                                                        isPaid: false,
                                                        isObligatory: fee.indicateur ?? false,
                                                        montantEncaisseAjour: fee.montantEncaisseAjour?.toInt(),
                                                        dateEchance: fee.dateEchance,
                                                        dateQuittance: fee.dateQuittance,
                                                        numeroQuittance: fee.numeroQuittance,
                                                    );
                                                }).toList() ?? [],
                                            )
                                        : const SizedBox.shrink(),
                            ),
                        ],
                    );
                }

                return const SizedBox.shrink();
            },
        );
    }

    Widget _buildNewControlBtn() {
        return Container(
            width: 335,
            height: 50,
            decoration: BoxDecoration(
                color: const Color(0xFF06B6E4),
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                    BoxShadow(
                        color: const Color(0xFF16196E).withOpacity(0.08),
                        offset: const Offset(0, 6),
                        blurRadius: 16,
                    ),
                ],
            ),
            child: Material(
                color: Colors.transparent,
                child: InkWell(
                    borderRadius: BorderRadius.circular(10),
                    onTap: () async {
                        // Handle new control action
                        await _handleNewControl();
                    },
                    child: const Center(
                        child: Text(
                            'NOUVEAU CONTRÔLE',
                            style: TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                                color: Colors.white,
                            ),
                        ),
                    ),
                ),
            ),
        );
    }

    Future<void> _handleNewControl() async {
        await QRScannerService.launchScanner(
            context: context,
            onQRScanned: (matricule) async {
                // Handle QR code scan result
                await _updateWithNewMatricule(matricule);

                if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                        CustomSnackbar(message: 'Nouveau contrôle effectué avec succès!', isError: false).getSnackBar(),
                    );
                }
            },
            onManualInput: (matricule) async {
                // Handle manual input result
                await _updateWithNewMatricule(matricule);

                if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                        CustomSnackbar(message: 'Nouveau contrôle effectué avec succès!', isError: false).getSnackBar(),
                    );
                }
            },
            onError: () {
                if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                        CustomSnackbar(message: 'Erreur lors du contrôle. Veuillez réessayer.', isError: true).getSnackBar(),
                    );
                }
            },
        );
    }

    Future<void> _updateWithNewMatricule(String matricule) async {
        // Update the current matricule and re-initialize financial check
        setState(() {
            _currentMatricule = matricule;
            _currentCarteVirtuelle = null; // Reset current carte virtuelle
        });

        // Re-initialize financial check with the new matricule
        await _initializeFinancialCheck();
        debugPrint('ControleFinancierPage: Financial check initialized with new matricule: $matricule');
    }
}