import 'dart:convert';

import 'package:dio/dio.dart';
import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../../../../core/api/api_exception.dart';
import '../../../../core/error/exceptions.dart';
import '../../../authentication/data/models/deconnexion_request.dart';
import '../../../authentication/data/models/delete_account_request.dart'; // New import
import 'package:flutter/material.dart';
import 'package:kairos/features/profile/data/models/carte_virtuelle_model.dart';

/// Abstract interface for profile remote data source
abstract class ProfileRemoteDataSource {
  /// Call suppressionEtablissement API for logout
  Future<void> logout(DeconnexionRequest request);

  /// Call suppressionCompte API for account deletion
  Future<void> deleteAccount(DeleteAccountRequest request); // New method

  /// Get virtual card data from the API
  Future<CarteVirtuelleModel> getCarteVirtuelle({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });
}

/// Implementation of ProfileRemoteDataSource
class ProfileRemoteDataSourceImpl implements ProfileRemoteDataSource {
  final ApiClient apiClient;

  ProfileRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<void> logout(DeconnexionRequest request) async {
    try {
      // Make HTTP POST request to the suppressionEtablissement endpoint
      final response = await apiClient.postWithToken(
        ApiEndpoints.suppressionEtablissement,
        data: request.toJson(),
      );

      // Check if the response is successful
      if (response.statusCode != 200) {
        throw ServerException('Failed to logout with status code: ${response.statusCode}');
      }

    } on DioException catch (e) {
      if (e.response != null) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion lors de la déconnexion: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la déconnexion: $e');
    }
  }
  @override
  Future<void> deleteAccount(DeleteAccountRequest request) async {
    try {
      final response = await apiClient.postWithToken(
        ApiEndpoints.suppressionCompte,
        data: request.toJson(),
      );

      if (response.statusCode != 200) {
        throw ServerException('Failed to delete account with status code: ${response.statusCode}');
      }

      // Check for specific success return code if the API provides one
      if (response.data is Map && response.data['returnCode'] != 'SUCCESS') {
        throw ServerException(response.data['message'] ?? 'Failed to delete account: Unknown error');
      }

    } on DioException catch (e) {
      if (e.response != null) {
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        throw NetworkException('Erreur de connexion lors de la suppression du compte: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la suppression du compte: $e');
    }
  }

  @override
  Future<CarteVirtuelleModel> getCarteVirtuelle({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParameters = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
      };

      // Add codeUtilisateur for PAR profile if provided
      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParameters['codeUtilisateur'] = codeUtilisateur;
      }

      // Make HTTP POST request to the infosCarteVirtuelle endpoint
      final responseBytes = await apiClient.getWithToken(
        ApiEndpoints.infosCarteVirtuelle, // Use the correct API endpoint
        queryParameters: queryParameters, // Send parameters in the request body
        options: Options(responseType: ResponseType.bytes)
      );

      final decodedResponse = latin1.decode(responseBytes.data);
      final response = jsonDecode(decodedResponse);
      debugPrint('ProfileRemoteDataSourceImpl: getCarteVirtuelle response: $response');
      if (responseBytes.statusCode == 200 && response is Map) {
        return CarteVirtuelleModel.fromJson(response as Map<String, dynamic>);
      } else {
        throw ServerException('Failed to load virtual card data: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        throw NetworkException('Erreur de connexion lors de la récupération de la carte virtuelle: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la récupération de la carte virtuelle: $e');
    }
  }
}
