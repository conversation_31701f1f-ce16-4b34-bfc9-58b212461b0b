import 'package:kairos/features/grades/domain/entities/notes_evaluation_entity.dart';
import 'package:kairos/features/grades/presentation/bloc/notes_cubit.dart';
import 'package:kairos/features/grades/presentation/bloc/notes_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/enums/header_enums.dart';
import 'package:kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:kairos/features/grades/presentation/pages/notes/note_item.widget.dart';
import 'package:kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:kairos/core/widgets/common/empty_message.widget.dart';
import 'package:kairos/core/utils/date_utils.dart' as date_utils;
import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/core/di/injection_container.dart';


class NotesPage extends StatefulWidget {
  final EtablissementUtilisateur? school;
  final EnfantTuteurEntity? etudiant;

  const NotesPage({
    super.key,
    this.school,
    this.etudiant,
  });

  @override
  State<NotesPage> createState() => _NotesPageState();
}

class _NotesPageState extends State<NotesPage> with SingleTickerProviderStateMixin {
  bool _isSearchBarVisible = false;
  late TextEditingController _searchController;
  List<NotesEvaluationEntity> _filteredNotes = [];
  List<NotesEvaluationEntity> _allNotes = [];

  // Date filter state
  String? _startDateFilter;
  String? _endDateFilter;

  // Animation controller
  late AnimationController _searchAnimationController;

  // Data sources
  final AuthLocalDataSource _authLocalDataSource = sl<AuthLocalDataSource>();

  // Navigation arguments
  EtablissementUtilisateur? _school;
  EnfantTuteurEntity? _etudiant;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Add listener to filter notes when search text changes
    _searchController.addListener(_filterNotes);

    // Load navigation arguments and initialize data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadArgumentsAndData();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  /// Load navigation arguments and initialize data
  void _loadArgumentsAndData() {
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    
    if (args != null) {
      _school = args['school'] as EtablissementUtilisateur?;
      _etudiant = args['etudiant'] as EnfantTuteurEntity?;
    } else {
      // Use constructor parameters if no route arguments
      _school = widget.school;
      _etudiant = widget.etudiant;
    }

    // Load notes data
    _loadNotesData();
  }

  /// Load notes data using BLoC
  Future<void> _loadNotesData() async {
    if (_school == null) return;

    try {
      final phoneNumber = await _authLocalDataSource.getPhoneNumber();
      if (phoneNumber != null && mounted) {
        // Determine parameters based on user profile
        final codeEtudiant = _etudiant?.codeEtudiant ?? _school!.codeUtilisateur;
        final codeUtilisateur = _etudiant != null ? _school!.codeUtilisateur : null;

        context.read<NotesCubit>().loadNotesEvaluations(
          codeEtab: _school!.codeEtab,
          telephone: phoneNumber,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
        );
      }
    } catch (e) {
      debugPrint('Error loading notes data: $e');
    }
  }

  /// Method to filter notes based on search query and date range
  void _filterNotes() {
    final String query = _searchController.text.toLowerCase();
    setState(() {
      _filteredNotes = _allNotes.where((note) {
        // Text search filter
        bool matchesText = note.typeDevoir.toLowerCase().contains(query) ||
               note.cours.toLowerCase().contains(query) ||
               note.professeur.toLowerCase().contains(query) ||
               note.dateDevoir.toLowerCase().contains(query) ||
               note.note.toString().contains(query) ||
               note.semestre.toLowerCase().contains(query);

        // Date range filter
        bool matchesDateRange = true;
        if (_startDateFilter != null && _endDateFilter != null) {
          try {
            final noteDate = DateTime.parse(note.dateDevoir);
            final startDate = date_utils.parseDate(_startDateFilter!);
            final endDate = date_utils.parseDate(_endDateFilter!);
            
              matchesDateRange = noteDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
                               noteDate.isBefore(endDate.add(const Duration(days: 1)));
          } catch (e) {
            // If date parsing fails, include the item
            matchesDateRange = true;
          }
        }

        return matchesText && matchesDateRange;
      }).toList();
    });
  }

  /// Handle date filter change
  void _onDateFilterChanged(Map<String, String> dateRange) {
    setState(() {
      _startDateFilter = dateRange['startDate'];
      _endDateFilter = dateRange['endDate'];
    });
    _filterNotes();
  }

  /// Clear date filter
  void _clearDateFilter() {
    setState(() {
      _startDateFilter = null;
      _endDateFilter = null;
    });
    _filterNotes();
  }

  /// Method to toggle search bar visibility
  void _toggleSearchBarVisibility() {
    setState(() {
      _isSearchBarVisible = !_isSearchBarVisible;
      if (!_isSearchBarVisible) {
        _searchAnimationController.reverse();
        _searchController.clear();
        _startDateFilter = null;
        _endDateFilter = null;
        _filterNotes();
      } else {
        _searchAnimationController.forward();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: false,
      body: CustomScrollView(
        slivers: [
          CustomAppBar(
            isSearchBarVisible: _isSearchBarVisible,
            pageSection: HeaderEnum.notes,
            title: "ÉVALUATIONS & NOTES",
            onSearchTap: _toggleSearchBarVisibility,
            etablissementUtilisateur: widget.school,
            enfantDuTuteur: widget.etudiant,
          ),
          // Search bar
          AnimatedBuilder(
            animation: _searchAnimationController,
            builder: (context, child) {
              return SliverPersistentHeader(
                delegate: SearchBarSliver(
                  extentHeight: _searchAnimationController.value * (_startDateFilter != null && _endDateFilter != null ? 88.0 : 60.0),
                  searchController: _searchController,
                  onSearchChanged: (query) => _filterNotes(),
                  onDateFilterChanged: _onDateFilterChanged,
                  onClearDateFilter: _clearDateFilter,
                  hasActiveFilter: _startDateFilter != null && _endDateFilter != null,
                  hintText: "Rechercher notes et évaluations...",
                  startDate: _startDateFilter,
                  endDate: _endDateFilter,
                ),
                pinned: true,
              );
            }
          ),
          // Content based on BLoC state
          BlocBuilder<NotesCubit, NotesState>(
            builder: (context, state) {
              if (state is NotesLoading) {
                return SliverFillRemaining(
                  child: Center(
                    child: CustomSpinner(
                      size: 60.0,
                      strokeWidth: 5.0,
                    ),
                  ),
                );
              } else if (state is NotesError) {
                return SliverFillRemaining(
                  child: Center(
                    child: EmptyMessage(message: state.message),
                  ),
                );
              } else if (state is NotesLoaded) {
                _allNotes = state.notesEvaluations;
                if (_filteredNotes.isEmpty && _searchController.text.isEmpty && _startDateFilter == null) {
                  _filteredNotes = _allNotes;
                }
                
                if (_filteredNotes.isNotEmpty) {
                  return SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final note = _filteredNotes[index];
                        return NoteItem(notesEvaluation: note);
                      },
                      childCount: _filteredNotes.length,
                    ),
                  );
                } else {
                  return SliverFillRemaining(
                    child: Center(
                      child: EmptyMessage(message: "Aucune note trouvée"),
                    ),
                  );
                }
              } else {
                return SliverFillRemaining(
                  child: Center(
                    child: EmptyMessage(message: "Aucune note trouvée"),
                  ),
                );
              }
            },
          ),
        ]
      )
    );
  }
}
